"""
Valorant Local Client API interface for detecting live matches.
This module handles communication with the local Valorant client to get live match data.
"""

import requests
import json
import base64
import psutil
import re
import os
import logging
from typing import Optional, Dict, List, Any
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# Disable SSL warnings for local client connections
urllib3.disable_warnings(InsecureRequestWarning)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ValorantClientError(Exception):
    """Exception raised when there's an error with the Valorant client connection."""
    pass


class ValorantLocalClient:
    """Interface to communicate with the local Valorant client."""
    
    def __init__(self):
        self.base_url = None
        self.headers = None
        self.session = requests.Session()
        self.session.verify = False  # Local client uses self-signed certificates
        
    def _find_valorant_process(self) -> Optional[Dict[str, str]]:
        """
        Find the running Valorant process and extract connection details.

        Returns:
            Dictionary with 'port', 'password', and 'pid' if found, None otherwise
        """
        logger.info("Searching for Valorant process...")
        valorant_processes = []

        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                        valorant_processes.append(proc.info)
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        logger.info(f"Found Valorant process (PID: {proc.info['pid']})")
                        logger.debug(f"Command line: {cmdline}")

                        # Extract port from command line (try both formats)
                        port_match = re.search(r'-remoting-app-port=(\d+)', cmdline) or re.search(r'--app-port=(\d+)', cmdline)
                        # Extract auth token from command line (try both formats) - allow more characters
                        token_match = re.search(r'-remoting-auth-token=([a-zA-Z0-9_-]+)', cmdline) or re.search(r'--app-token=([a-zA-Z0-9_-]+)', cmdline)

                        logger.debug(f"Port match: {port_match.group(1) if port_match else 'None'}")
                        logger.debug(f"Token match: {'Found' if token_match else 'None'}")

                        # Additional debugging for regex matching
                        if not port_match:
                            logger.debug(f"Port regex failed. Looking for patterns in: {cmdline}")
                        if not token_match:
                            logger.debug(f"Token regex failed. Looking for patterns in: {cmdline}")
                            # Try to find the token manually
                            if '-remoting-auth-token=' in cmdline:
                                token_start = cmdline.find('-remoting-auth-token=') + len('-remoting-auth-token=')
                                token_part = cmdline[token_start:token_start+50]  # Get next 50 chars
                                logger.debug(f"Found token part: {token_part}")


                        if port_match and token_match:
                            try:
                                result = {
                                    'port': port_match.group(1),
                                    'password': token_match.group(1),
                                    'pid': str(proc.info['pid'])
                                }
                                logger.info(f"Successfully extracted connection details: port={result['port']}, pid={result['pid']}")
                                return result
                            except KeyError as e:
                                logger.error(f"Missing process info key: {e}")
                                continue
                        else:
                            logger.warning(f"Valorant process found but missing connection details (port: {bool(port_match)}, token: {bool(token_match)})")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                    logger.debug(f"Process access error: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error finding Valorant process: {e}")

        if not valorant_processes:
            logger.warning("No Valorant processes found")
        else:
            logger.warning(f"Found {len(valorant_processes)} Valorant process(es) but none had valid connection details")

        return None
    
    def connect(self) -> bool:
        """
        Connect to the local Valorant client.

        Returns:
            True if connection successful, False otherwise
        """
        logger.info("Attempting to connect to Valorant client...")

        client_info = self._find_valorant_process()
        if not client_info:
            logger.error("Cannot connect: No Valorant process with valid connection details found")
            return False

        port = client_info['port']
        password = client_info['password']

        # Set up connection details
        self.base_url = f"https://127.0.0.1:{port}"
        logger.info(f"Connecting to: {self.base_url}")

        # Create authorization header
        auth_string = f"riot:{password}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        self.headers = {
            'Authorization': f'Basic {auth_b64}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        # Test connection
        test_url = f"{self.base_url}/entitlements/v1/token"
        logger.info(f"Testing connection to: {test_url}")

        try:
            response = self.session.get(
                test_url,
                headers=self.headers,
                timeout=5
            )
            logger.info(f"Connection test response: {response.status_code}")

            if response.status_code == 200:
                logger.info("Successfully connected to Valorant client")
                return True
            else:
                logger.error(f"Connection failed with status code: {response.status_code}")
                logger.error(f"Response text: {response.text}")
                return False

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error: {e}")
            return False
        except requests.exceptions.Timeout as e:
            logger.error(f"Connection timeout: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during connection: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Check if we're still connected to the client."""
        if not self.base_url or not self.headers:
            return False
            
        try:
            response = self.session.get(
                f"{self.base_url}/entitlements/v1/token",
                headers=self.headers,
                timeout=2
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_current_game_state(self) -> Optional[str]:
        """
        Get the current game state.
        
        Returns:
            Game state string ('PREGAME', 'INGAME', 'MENUS', etc.) or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/chat/v1/session",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('state', 'MENUS')
                
        except Exception as e:
            print(f"Error getting game state: {e}")
            
        return None
    
    def get_pregame_match(self) -> Optional[Dict[str, Any]]:
        """
        Get pregame match information.
        
        Returns:
            Pregame match data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/pregame/v1/players",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting pregame match: {e}")
            
        return None
    
    def get_current_match(self) -> Optional[Dict[str, Any]]:
        """
        Get current match information.
        
        Returns:
            Current match data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/core-game/v1/players",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting current match: {e}")
            
        return None
    
    def get_player_loadout(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get player loadout information.
        
        Args:
            player_uuid: Player's UUID
            
        Returns:
            Player loadout data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/personalization/v2/players/{player_uuid}/playerloadout",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting player loadout: {e}")
            
        return None
    
    def get_match_details(self, match_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed match information.
        
        Args:
            match_id: Match ID
            
        Returns:
            Match details or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/core-game/v1/matches/{match_id}",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting match details: {e}")
            
        return None

    def debug_connection(self) -> Dict[str, Any]:
        """
        Debug connection issues and return detailed information.

        Returns:
            Dictionary with debug information
        """
        debug_info = {
            'valorant_running': False,
            'process_found': False,
            'connection_details_found': False,
            'connection_successful': False,
            'processes': [],
            'errors': []
        }

        try:
            # Check if Valorant is running
            valorant_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                        valorant_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            debug_info['valorant_running'] = len(valorant_processes) > 0
            debug_info['processes'] = valorant_processes
            debug_info['process_found'] = len(valorant_processes) > 0

            if valorant_processes:
                # Try to find connection details
                client_info = self._find_valorant_process()
                debug_info['connection_details_found'] = client_info is not None

                if client_info:
                    # Try to connect
                    debug_info['connection_successful'] = self.connect()
                else:
                    debug_info['errors'].append("No connection details found in command line arguments")
            else:
                debug_info['errors'].append("No Valorant processes found")

        except Exception as e:
            debug_info['errors'].append(f"Debug error: {str(e)}")

        return debug_info


def is_valorant_running() -> bool:
    """Check if Valorant is currently running."""
    try:
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                return True
    except Exception:
        pass
    return False
