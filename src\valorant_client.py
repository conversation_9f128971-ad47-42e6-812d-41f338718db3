"""
Valorant Local Client API interface for detecting live matches.
This module handles communication with the local Valorant client to get live match data.
"""

import requests
import json
import base64
import psutil
import re
import os
from typing import Optional, Dict, List, Any
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# Disable SSL warnings for local client connections
urllib3.disable_warnings(InsecureRequestWarning)


class ValorantClientError(Exception):
    """Exception raised when there's an error with the Valorant client connection."""
    pass


class ValorantLocalClient:
    """Interface to communicate with the local Valorant client."""
    
    def __init__(self):
        self.base_url = None
        self.headers = None
        self.session = requests.Session()
        self.session.verify = False  # Local client uses self-signed certificates
        
    def _find_valorant_process(self) -> Optional[Dict[str, str]]:
        """
        Find the running Valorant process and extract connection details.
        
        Returns:
            Dictionary with 'port', 'password', and 'pid' if found, None otherwise
        """
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                        cmdline = ' '.join(proc.info['cmdline'])
                        
                        # Extract port from command line
                        port_match = re.search(r'--app-port=(\d+)', cmdline)
                        # Extract auth token from command line  
                        token_match = re.search(r'--app-token=([a-zA-Z0-9_-]+)', cmdline)
                        
                        if port_match and token_match:
                            return {
                                'port': port_match.group(1),
                                'password': token_match.group(1),
                                'pid': str(proc.info['pid'])
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            print(f"Error finding Valorant process: {e}")
            
        return None
    
    def connect(self) -> bool:
        """
        Connect to the local Valorant client.
        
        Returns:
            True if connection successful, False otherwise
        """
        client_info = self._find_valorant_process()
        if not client_info:
            return False
            
        port = client_info['port']
        password = client_info['password']
        
        # Set up connection details
        self.base_url = f"https://127.0.0.1:{port}"
        
        # Create authorization header
        auth_string = f"riot:{password}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        self.headers = {
            'Authorization': f'Basic {auth_b64}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # Test connection
        try:
            response = self.session.get(
                f"{self.base_url}/entitlements/v1/token",
                headers=self.headers,
                timeout=5
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def is_connected(self) -> bool:
        """Check if we're still connected to the client."""
        if not self.base_url or not self.headers:
            return False
            
        try:
            response = self.session.get(
                f"{self.base_url}/entitlements/v1/token",
                headers=self.headers,
                timeout=2
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_current_game_state(self) -> Optional[str]:
        """
        Get the current game state.
        
        Returns:
            Game state string ('PREGAME', 'INGAME', 'MENUS', etc.) or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/chat/v1/session",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('state', 'MENUS')
                
        except Exception as e:
            print(f"Error getting game state: {e}")
            
        return None
    
    def get_pregame_match(self) -> Optional[Dict[str, Any]]:
        """
        Get pregame match information.
        
        Returns:
            Pregame match data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/pregame/v1/players",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting pregame match: {e}")
            
        return None
    
    def get_current_match(self) -> Optional[Dict[str, Any]]:
        """
        Get current match information.
        
        Returns:
            Current match data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/core-game/v1/players",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting current match: {e}")
            
        return None
    
    def get_player_loadout(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get player loadout information.
        
        Args:
            player_uuid: Player's UUID
            
        Returns:
            Player loadout data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/personalization/v2/players/{player_uuid}/playerloadout",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting player loadout: {e}")
            
        return None
    
    def get_match_details(self, match_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed match information.
        
        Args:
            match_id: Match ID
            
        Returns:
            Match details or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/core-game/v1/matches/{match_id}",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting match details: {e}")
            
        return None


def is_valorant_running() -> bool:
    """Check if Valorant is currently running."""
    try:
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                return True
    except Exception:
        pass
    return False
