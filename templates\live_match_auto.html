{% extends "base.html" %}

{% block title %}Live Match Detector - Valorant Rank Checker{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="hero-title">
            <i class="bi bi-broadcast-pin"></i> Live Match Detector
        </h1>
        <p class="lead mb-0">Automatically detect and show ranks of players in your current Valorant match</p>
        <small class="text-muted">Works like Toolorant - automatically detects when you're in a match!</small>
    </div>

    <!-- Status Section -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-activity"></i> Detection Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="status-item">
                                <div id="valorant-status" class="status-indicator">
                                    {% if valorant_running %}
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                    {% else %}
                                    <i class="bi bi-x-circle-fill text-danger"></i>
                                    {% endif %}
                                </div>
                                <h6>Valorant</h6>
                                <small id="valorant-status-text" class="text-muted">
                                    {% if valorant_running %}Running{% else %}Not Running{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="status-item">
                                <div id="detection-status" class="status-indicator">
                                    <i class="bi bi-dash-circle text-secondary"></i>
                                </div>
                                <h6>Detection</h6>
                                <small id="detection-status-text" class="text-muted">Stopped</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="status-item">
                                <div id="match-status" class="status-indicator">
                                    <i class="bi bi-dash-circle text-secondary"></i>
                                </div>
                                <h6>Match Status</h6>
                                <small id="match-status-text" class="text-muted">No Match</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="status-item">
                                <button id="toggle-detection" class="btn btn-primary" disabled>
                                    <i class="bi bi-play-fill"></i> Start Detection
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Match Information -->
    <div id="match-info" class="row" style="display: none;">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-controller"></i> Current Match
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Map:</strong> <span id="match-map">Unknown</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Mode:</strong> <span id="match-mode">Unknown</span>
                        </div>
                        <div class="col-md-4">
                            <strong>State:</strong> <span id="match-state">Unknown</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Players -->
            <div class="row">
                <!-- Red Team -->
                <div class="col-lg-6 mb-4">
                    <div class="card team-red">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-circle-fill text-danger"></i> Red Team
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="red-team-players">
                                <!-- Red team players will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blue Team -->
                <div class="col-lg-6 mb-4">
                    <div class="card team-blue">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-circle-fill text-primary"></i> Blue Team
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="blue-team-players">
                                <!-- Blue team players will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div id="instructions" class="row justify-content-center">
        <div class="col-md-8">
            <div class="alert alert-info" role="alert">
                <h5 class="alert-heading">
                    <i class="bi bi-info-circle"></i> How to use Live Match Detector
                </h5>
                <ol class="mb-0">
                    <li><strong>Launch Valorant</strong> and make sure it's running</li>
                    <li><strong>Click "Start Detection"</strong> to begin monitoring</li>
                    <li><strong>Join a match</strong> (Competitive, Unrated, etc.)</li>
                    <li><strong>View player ranks</strong> automatically when the match starts</li>
                </ol>
                <hr>
                <p class="mb-0">
                    <strong>Note:</strong> This tool works like Toolorant by connecting to your local Valorant client. 
                    It automatically detects when you enter a match and shows all players' ranks and information.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .status-item {
        padding: 1rem;
    }
    
    .status-indicator {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .player-card {
        transition: all 0.3s ease;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .player-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .rank {
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .team-red .card-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    }
    
    .team-blue .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }
    
    .loading-spinner {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
let isDetecting = false;
let statusInterval = null;

function updateStatus() {
    fetch('/api/live-match/status')
        .then(response => response.json())
        .then(data => {
            // Update Valorant status
            const valorantStatus = document.getElementById('valorant-status');
            const valorantStatusText = document.getElementById('valorant-status-text');
            
            if (data.valorant_running) {
                valorantStatus.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
                valorantStatusText.textContent = 'Running';
            } else {
                valorantStatus.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i>';
                valorantStatusText.textContent = 'Not Running';
            }
            
            // Update detection status
            const detectionStatus = document.getElementById('detection-status');
            const detectionStatusText = document.getElementById('detection-status-text');
            
            if (data.is_monitoring) {
                detectionStatus.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
                detectionStatusText.textContent = 'Active';
                isDetecting = true;
            } else {
                detectionStatus.innerHTML = '<i class="bi bi-dash-circle text-secondary"></i>';
                detectionStatusText.textContent = 'Stopped';
                isDetecting = false;
            }
            
            // Update match status
            const matchStatus = document.getElementById('match-status');
            const matchStatusText = document.getElementById('match-status-text');
            
            if (data.in_match && data.current_match) {
                matchStatus.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
                matchStatusText.textContent = data.current_match.state;
                showMatchInfo(data.current_match);
            } else {
                matchStatus.innerHTML = '<i class="bi bi-dash-circle text-secondary"></i>';
                matchStatusText.textContent = 'No Match';
                hideMatchInfo();
            }
            
            // Update toggle button
            const toggleBtn = document.getElementById('toggle-detection');
            toggleBtn.disabled = !data.valorant_running;
            
            if (data.is_monitoring) {
                toggleBtn.innerHTML = '<i class="bi bi-stop-fill"></i> Stop Detection';
                toggleBtn.className = 'btn btn-danger';
            } else {
                toggleBtn.innerHTML = '<i class="bi bi-play-fill"></i> Start Detection';
                toggleBtn.className = 'btn btn-primary';
            }
        })
        .catch(error => {
            console.error('Error updating status:', error);
        });
}

function showMatchInfo(matchData) {
    document.getElementById('match-info').style.display = 'block';
    document.getElementById('instructions').style.display = 'none';
    
    // Update match details
    document.getElementById('match-map').textContent = matchData.map || 'Unknown';
    document.getElementById('match-mode').textContent = matchData.mode || 'Unknown';
    document.getElementById('match-state').textContent = matchData.state || 'Unknown';
    
    // Update players
    const redTeamDiv = document.getElementById('red-team-players');
    const blueTeamDiv = document.getElementById('blue-team-players');
    
    redTeamDiv.innerHTML = '';
    blueTeamDiv.innerHTML = '';
    
    if (matchData.players) {
        matchData.players.forEach(player => {
            const playerCard = createPlayerCard(player);
            if (player.team === 'Red') {
                redTeamDiv.appendChild(playerCard);
            } else {
                blueTeamDiv.appendChild(playerCard);
            }
        });
    }
}

function hideMatchInfo() {
    document.getElementById('match-info').style.display = 'none';
    document.getElementById('instructions').style.display = 'block';
}

function createPlayerCard(player) {
    const card = document.createElement('div');
    card.className = 'player-card';
    
    const rankClass = 'rank-' + (player.rank || 'unrated').toLowerCase().replace(' ', '-');
    
    card.innerHTML = `
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="mb-1">${player.name}#${player.tag}</h6>
                <span class="badge bg-secondary">${player.agent}</span>
            </div>
            <div class="col-md-3">
                <div class="rank-info">
                    <span class="rank ${rankClass}">${player.rank}</span>
                    ${player.rr > 0 ? `<small class="text-muted d-block">${player.rr} RR</small>` : ''}
                </div>
            </div>
            <div class="col-md-3">
                <div class="level">
                    <small class="text-muted">Level ${player.level}</small>
                </div>
            </div>
        </div>
    `;
    
    return card;
}

function toggleDetection() {
    const toggleBtn = document.getElementById('toggle-detection');
    
    if (isDetecting) {
        // Stop detection
        toggleBtn.disabled = true;
        toggleBtn.innerHTML = '<span class="loading-spinner"></span> Stopping...';
        
        fetch('/api/live-match/stop')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatus();
                } else {
                    alert('Error stopping detection: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error stopping detection:', error);
                alert('Error stopping detection');
            })
            .finally(() => {
                toggleBtn.disabled = false;
            });
    } else {
        // Start detection
        toggleBtn.disabled = true;
        toggleBtn.innerHTML = '<span class="loading-spinner"></span> Starting...';
        
        fetch('/api/live-match/start')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatus();
                } else {
                    alert('Error starting detection: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error starting detection:', error);
                alert('Error starting detection');
            })
            .finally(() => {
                toggleBtn.disabled = false;
            });
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Set up toggle button
    document.getElementById('toggle-detection').addEventListener('click', toggleDetection);
    
    // Initial status update
    updateStatus();
    
    // Start status polling
    statusInterval = setInterval(updateStatus, 3000); // Update every 3 seconds
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (statusInterval) {
        clearInterval(statusInterval);
    }
});
</script>
{% endblock %}
