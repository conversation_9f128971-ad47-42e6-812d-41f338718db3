#!/usr/bin/env python3
"""
Valorant Rank Checker Web Application

A Flask web application to check player ranks and match history using the HenrikDev API.
"""

import sys
import os
from flask import Flask, render_template, request, jsonify, redirect, url_for
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api_client import ValorantAPI, APIError, RateLimitError
from src.models import Player, MatchInfo
from src.utils import parse_riot_id, get_rank_color
from src.config import config
from src.live_match_detector import LiveMatchDetector
from src.valorant_client import is_valorant_running

app = Flask(__name__)
app.secret_key = 'valorant-rank-checker-secret-key'

# Initialize API client
api = ValorantAPI()

# Initialize live match detector
live_detector = LiveMatchDetector(api)


@app.route('/')
def index():
    """Home page with player lookup form."""
    return render_template('index.html')


@app.route('/player/<name>/<tag>')
def player_profile(name, tag):
    """Player profile page with rank info and match history."""
    try:
        # Get player info
        player = api.get_player_full_info(name, tag)
        if not player:
            return render_template('error.html', 
                                 error="Player not found", 
                                 message=f"Could not find player {name}#{tag}")
        
        # Get match history
        try:
            match_ids = api.get_match_history(name, tag, size=10)
            matches = []
            
            for match_id in match_ids[:5]:  # Limit to 5 matches to avoid rate limits
                try:
                    match_info = api.get_match_details(match_id)
                    if match_info:
                        # Find the player in the match
                        player_match_data = None
                        for p in match_info.all_players:
                            if p.name.lower() == name.lower() and p.tag.lower() == tag.lower():
                                player_match_data = p
                                break
                        
                        if player_match_data:
                            matches.append({
                                'match_info': match_info,
                                'player_data': player_match_data,
                                'match_id': match_id
                            })
                except (APIError, RateLimitError):
                    continue  # Skip failed matches
                    
        except (APIError, RateLimitError):
            matches = []
        
        return render_template('player.html', 
                             player=player, 
                             matches=matches,
                             get_rank_color=get_rank_color)
        
    except RateLimitError:
        return render_template('error.html', 
                             error="Rate Limit Exceeded", 
                             message="Please wait a moment before making another request.")
    except APIError as e:
        return render_template('error.html', 
                             error="API Error", 
                             message=str(e))
    except Exception as e:
        return render_template('error.html', 
                             error="Unexpected Error", 
                             message=f"An unexpected error occurred: {str(e)}")


@app.route('/match/<region>/<match_id>')
def match_details(region, match_id):
    """Match details page showing all players."""
    try:
        match_info = api.get_match_details(match_id, region)
        if not match_info:
            return render_template('error.html', 
                                 error="Match not found", 
                                 message=f"Could not find match {match_id}")
        
        return render_template('match.html', 
                             match=match_info,
                             get_rank_color=get_rank_color)
        
    except RateLimitError:
        return render_template('error.html', 
                             error="Rate Limit Exceeded", 
                             message="Please wait a moment before making another request.")
    except APIError as e:
        return render_template('error.html', 
                             error="API Error", 
                             message=str(e))
    except Exception as e:
        return render_template('error.html', 
                             error="Unexpected Error", 
                             message=f"An unexpected error occurred: {str(e)}")


@app.route('/search', methods=['POST'])
def search_player():
    """Handle player search form submission."""
    riot_id = request.form.get('riot_id', '').strip()
    
    if not riot_id:
        return render_template('error.html', 
                             error="Invalid Input", 
                             message="Please enter a Riot ID.")
    
    try:
        name, tag = parse_riot_id(riot_id)
        return redirect(url_for('player_profile', name=name, tag=tag))
    except ValueError as e:
        return render_template('error.html', 
                             error="Invalid Riot ID Format", 
                             message=str(e))


@app.route('/api/player/<name>/<tag>')
def api_player_info(name, tag):
    """API endpoint for player information."""
    try:
        player = api.get_player_full_info(name, tag)
        if not player:
            return jsonify({'error': 'Player not found'}), 404
        
        return jsonify({
            'riot_id': player.riot_id,
            'account_level': player.account_level,
            'platform': player.platform,
            'region': player.region,
            'current_rank': player.display_rank,
            'peak_rank': player.display_peak,
            'updated_at': player.updated_at.isoformat() if player.updated_at else None
        })
        
    except RateLimitError:
        return jsonify({'error': 'Rate limit exceeded'}), 429
    except APIError as e:
        return jsonify({'error': str(e)}), 500


@app.route('/batch')
def batch_lookup():
    """Batch player lookup page."""
    return render_template('batch.html')


@app.route('/live-match')
def live_match():
    """Live match automatic detection page"""
    valorant_running = is_valorant_running()
    current_match = live_detector.get_current_match_data() if valorant_running else None

    return render_template('live_match_auto.html',
                         valorant_running=valorant_running,
                         current_match=current_match)


@app.route('/live-match', methods=['POST'])
def live_match_post():
    """Handle live match lookup."""
    riot_id = request.form.get('riot_id', '').strip()

    if not riot_id:
        return render_template('live_match.html', error="Please enter a Riot ID")

    try:
        name, tag = parse_riot_id(riot_id)

        # Get player's recent matches to find potential current match
        recent_matches = api.get_recent_matches(name, tag, limit=1)

        if not recent_matches:
            return render_template('live_match.html',
                                 error="No recent matches found for this player",
                                 riot_id=riot_id)

        # Get the most recent match
        recent_match = recent_matches[0]

        # Check if the match is very recent (within last 2 hours)
        from datetime import datetime, timedelta
        match_time = datetime.fromtimestamp(recent_match.get('game_start_patched', 0) / 1000)
        time_diff = datetime.now() - match_time

        if time_diff > timedelta(hours=2):
            return render_template('live_match.html',
                                 error="No recent match found. The most recent match was too long ago.",
                                 riot_id=riot_id)

        # Get full match details
        match_details = api.get_raw_match_data(recent_match['metadata']['matchid'])

        if not match_details:
            return render_template('live_match.html',
                                 error="Could not retrieve match details",
                                 riot_id=riot_id)

        # Extract all players from the match
        all_players = []
        for player_data in match_details.get('players', []):
            player_name = player_data.get('name', 'Unknown')
            player_tag = player_data.get('tag', 'Unknown')

            # Get rank information for each player
            try:
                player_info = api.get_player_mmr(player_name, player_tag)
                if player_info:
                    all_players.append({
                        'name': player_name,
                        'tag': player_tag,
                        'rank': player_info.get('current_tier_patched', 'Unrated'),
                        'rr': player_info.get('ranking_in_tier', 0),
                        'team': player_data.get('team', 'Unknown'),
                        'agent': player_data.get('character', 'Unknown'),
                        'stats': {
                            'kills': player_data.get('stats', {}).get('kills', 0),
                            'deaths': player_data.get('stats', {}).get('deaths', 0),
                            'assists': player_data.get('stats', {}).get('assists', 0)
                        }
                    })
            except Exception:
                # If we can't get rank info, still include the player
                all_players.append({
                    'name': player_name,
                    'tag': player_tag,
                    'rank': 'Unknown',
                    'rr': 0,
                    'team': player_data.get('team', 'Unknown'),
                    'agent': player_data.get('character', 'Unknown'),
                    'stats': {
                        'kills': player_data.get('stats', {}).get('kills', 0),
                        'deaths': player_data.get('stats', {}).get('deaths', 0),
                        'assists': player_data.get('stats', {}).get('assists', 0)
                    }
                })

        return render_template('live_match.html',
                             players=all_players,
                             match_info={
                                 'map': match_details.get('metadata', {}).get('map', 'Unknown'),
                                 'mode': match_details.get('metadata', {}).get('mode', 'Unknown'),
                                 'started': match_time.strftime('%Y-%m-%d %H:%M:%S')
                             },
                             riot_id=riot_id)

    except ValueError:
        return render_template('live_match.html',
                             error="Invalid Riot ID format. Use: Name#Tag",
                             riot_id=riot_id)
    except RateLimitError:
        return render_template('live_match.html',
                             error="Rate limit exceeded. Please try again later.",
                             riot_id=riot_id)
    except APIError as e:
        return render_template('live_match.html',
                             error=f"API Error: {str(e)}",
                             riot_id=riot_id)


@app.route('/api/live-match/start')
def start_live_detection():
    """Start live match detection."""
    try:
        if not is_valorant_running():
            return jsonify({'success': False, 'error': 'Valorant is not running'})

        success = live_detector.start_monitoring()
        if success:
            return jsonify({'success': True, 'message': 'Live match detection started'})
        else:
            return jsonify({'success': False, 'error': 'Failed to connect to Valorant client'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/live-match/stop')
def stop_live_detection():
    """Stop live match detection."""
    try:
        live_detector.stop_monitoring()
        return jsonify({'success': True, 'message': 'Live match detection stopped'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/live-match/status')
def live_match_status():
    """Get current live match status."""
    try:
        valorant_running = is_valorant_running()
        current_match = live_detector.get_current_match_data()
        is_monitoring = live_detector.is_monitoring

        return jsonify({
            'valorant_running': valorant_running,
            'is_monitoring': is_monitoring,
            'in_match': live_detector.is_in_match(),
            'current_match': current_match
        })
    except Exception as e:
        return jsonify({'error': str(e)})


@app.route('/api/live-match/debug')
def debug_live_match():
    """Debug live match connection issues."""
    try:
        debug_info = live_detector.client.debug_connection()
        return jsonify(debug_info)
    except Exception as e:
        return jsonify({'error': str(e), 'debug_failed': True})


@app.route('/batch', methods=['POST'])
def batch_lookup_post():
    """Handle batch player lookup."""
    riot_ids_text = request.form.get('riot_ids', '').strip()
    
    if not riot_ids_text:
        return render_template('error.html', 
                             error="Invalid Input", 
                             message="Please enter at least one Riot ID.")
    
    # Parse multiple Riot IDs
    riot_ids = []
    for item in riot_ids_text.replace(',', ' ').split():
        item = item.strip()
        if item:
            riot_ids.append(item)
    
    if not riot_ids:
        return render_template('error.html', 
                             error="Invalid Input", 
                             message="No valid Riot IDs found.")
    
    players = []
    failed_lookups = []
    
    for riot_id in riot_ids[:10]:  # Limit to 10 players to avoid rate limits
        try:
            name, tag = parse_riot_id(riot_id)
            player = api.get_player_full_info(name, tag)
            
            if player:
                players.append(player)
            else:
                failed_lookups.append(riot_id)
                
        except ValueError:
            failed_lookups.append(riot_id)
        except (RateLimitError, APIError):
            failed_lookups.append(riot_id)
    
    return render_template('batch_results.html', 
                         players=players, 
                         failed_lookups=failed_lookups,
                         get_rank_color=get_rank_color)


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return render_template('error.html', 
                         error="Page Not Found", 
                         message="The page you're looking for doesn't exist."), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return render_template('error.html', 
                         error="Internal Server Error", 
                         message="An internal server error occurred."), 500


if __name__ == '__main__':
    try:
        # Test API configuration
        print("🚀 Starting Valorant Rank Checker Web App...")
        print(f"📡 API Key: {'Set' if config.api_key else 'Not Set'}")
        print(f"🌍 Default Region: {config.default_region.upper()}")
        print(f"💻 Default Platform: {config.default_platform.upper()}")
        
        if not config.api_key:
            print("❌ Error: API key not found. Please set HENRIKDEV_API_KEY in your .env file.")
            sys.exit(1)
        
        print("✅ Configuration looks good!")
        print("🌐 Starting web server at http://localhost:5000")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e:
        print(f"❌ Failed to start application: {str(e)}")
        sys.exit(1)
