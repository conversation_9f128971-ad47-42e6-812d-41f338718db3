"""Data models for Valorant Tool."""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime


@dataclass
class RankInfo:
    """Player rank information."""
    current_tier_id: int
    current_tier_name: str
    rr: int  # Rank Rating
    last_change: int
    elo: int
    games_needed_for_rating: int
    leaderboard_rank: Optional[int] = None
    
    @property
    def display_rank(self) -> str:
        """Get formatted rank display string."""
        if self.current_tier_name == "Unrated":
            return "Unrated"
        return f"{self.current_tier_name} ({self.rr} RR)"


@dataclass
class PeakRank:
    """Player's peak rank information."""
    tier_id: int
    tier_name: str
    season: str
    
    def __str__(self) -> str:
        return f"{self.tier_name} (Season {self.season})"


@dataclass
class Player:
    """Player information."""
    puuid: str
    name: str
    tag: str
    region: str
    account_level: int
    platform: str
    card_id: Optional[str] = None
    title_id: Optional[str] = None
    updated_at: Optional[datetime] = None
    
    # Rank information
    current_rank: Optional[RankInfo] = None
    peak_rank: Optional[PeakRank] = None
    
    @property
    def riot_id(self) -> str:
        """Get full Riot ID (name#tag)."""
        return f"{self.name}#{self.tag}"
    
    @property
    def display_rank(self) -> str:
        """Get formatted current rank."""
        if self.current_rank:
            return self.current_rank.display_rank
        return "No Competitive Data"

    @property
    def display_peak(self) -> str:
        """Get formatted peak rank."""
        if self.peak_rank:
            return str(self.peak_rank)
        return "No Competitive Data"


@dataclass
class MatchPlayer:
    """Player information in a match context."""
    puuid: str
    name: str
    tag: str
    team_id: str
    agent_name: str
    tier_id: int
    tier_name: str
    account_level: int
    platform: str
    
    # Match stats
    kills: int
    deaths: int
    assists: int
    score: int
    damage_dealt: int
    damage_received: int
    
    @property
    def riot_id(self) -> str:
        """Get full Riot ID (name#tag)."""
        return f"{self.name}#{self.tag}"
    
    @property
    def kda(self) -> str:
        """Get KDA ratio as string."""
        return f"{self.kills}/{self.deaths}/{self.assists}"


@dataclass
class MatchInfo:
    """Match information."""
    match_id: str
    map_name: str
    game_mode: str
    started_at: datetime
    game_length_ms: int
    region: str
    platform: str
    
    # Teams
    red_team: List[MatchPlayer]
    blue_team: List[MatchPlayer]
    
    # Results
    red_rounds_won: int
    blue_rounds_won: int
    red_team_won: bool
    
    @property
    def all_players(self) -> List[MatchPlayer]:
        """Get all players from both teams."""
        return self.red_team + self.blue_team
    
    @property
    def game_length_minutes(self) -> int:
        """Get game length in minutes."""
        return self.game_length_ms // (1000 * 60)
    
    @property
    def score_display(self) -> str:
        """Get formatted score display."""
        return f"{self.red_rounds_won} - {self.blue_rounds_won}"
